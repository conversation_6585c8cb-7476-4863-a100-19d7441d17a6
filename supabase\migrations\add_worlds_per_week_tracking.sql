-- Add worlds_per_week tracking to user_usage table
-- This migration extends the existing usage tracking to support weekly world creation limits

-- Add new columns for worlds per week tracking
ALTER TABLE public.user_usage 
ADD COLUMN IF NOT EXISTS worlds_this_week integer DEFAULT 0 NOT NULL,
ADD COLUMN IF NOT EXISTS worlds_reset_date date DEFAULT CURRENT_DATE NOT NULL;

-- Create index for efficient queries on worlds reset date
CREATE INDEX IF NOT EXISTS idx_user_usage_worlds_reset_date ON public.user_usage(worlds_reset_date);

-- Function to reset weekly world counts
CREATE OR REPLACE FUNCTION reset_weekly_worlds()
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    UPDATE public.user_usage
    SET
        worlds_this_week = 0,
        worlds_reset_date = CURRENT_DATE,
        updated_at = now()
    WHERE worlds_reset_date < DATE_TRUNC('week', CURRENT_DATE);
END;
$$;

-- Function to increment world count
CREATE OR REPLACE FUNCTION increment_world_count(p_user_id uuid)
RETURNS integer
LANGUAGE plpgsql
AS $$
DECLARE
    new_count integer;
BEGIN
    -- Ensure user usage record exists
    PERFORM initialize_user_usage(p_user_id);

    -- Reset count if it's a new week
    PERFORM reset_weekly_worlds();

    -- Increment world count
    UPDATE public.user_usage
    SET
        worlds_this_week = worlds_this_week + 1,
        updated_at = now()
    WHERE user_id = p_user_id
    RETURNING worlds_this_week INTO new_count;

    RETURN new_count;
END;
$$;

-- Function to get current world count for a user
CREATE OR REPLACE FUNCTION get_user_world_count_this_week(p_user_id uuid)
RETURNS integer
LANGUAGE plpgsql
AS $$
DECLARE
    current_count integer;
BEGIN
    -- Ensure user usage record exists
    PERFORM initialize_user_usage(p_user_id);

    -- Reset count if it's a new week
    PERFORM reset_weekly_worlds();

    -- Get current count
    SELECT worlds_this_week INTO current_count
    FROM public.user_usage
    WHERE user_id = p_user_id;

    RETURN COALESCE(current_count, 0);
END;
$$;

-- Update the initialize_user_usage function to include worlds tracking
CREATE OR REPLACE FUNCTION initialize_user_usage(p_user_id uuid)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
    INSERT INTO public.user_usage (
        user_id, 
        messages_this_month, 
        messages_reset_date,
        worlds_this_week,
        worlds_reset_date
    )
    VALUES (
        p_user_id, 
        0, 
        CURRENT_DATE,
        0,
        CURRENT_DATE
    )
    ON CONFLICT (user_id) DO UPDATE SET
        -- Ensure new columns are set if they were NULL
        worlds_this_week = COALESCE(user_usage.worlds_this_week, 0),
        worlds_reset_date = COALESCE(user_usage.worlds_reset_date, CURRENT_DATE);
END;
$$;

-- Comments for documentation
COMMENT ON COLUMN public.user_usage.worlds_this_week IS 'Number of worlds created this week';
COMMENT ON COLUMN public.user_usage.worlds_reset_date IS 'Date when world count was last reset';
COMMENT ON FUNCTION increment_world_count(uuid) IS 'Increments the weekly world count for a user';
COMMENT ON FUNCTION get_user_world_count_this_week(uuid) IS 'Gets the current weekly world count for a user';
COMMENT ON FUNCTION reset_weekly_worlds() IS 'Resets weekly world counts for all users when a new week starts';
