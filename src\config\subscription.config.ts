import { SubscriptionTierConfig } from '@/types/subscription.types';
import { SubscriptionTier } from '@/types/schema.types';

/**
 * Centralized subscription tier configuration
 * This is the single source of truth for all subscription-related settings
 */
export const SUBSCRIPTION_TIERS: Record<SubscriptionTier, SubscriptionTierConfig> = {
  free: {
    tier: 'free',
    name: 'free',
    displayName: 'Free',
    price: {
      monthly: 0,
    },
    description: 'Perfect for getting started with solo RPG adventures',
    limits: {
      worlds_per_week: 2,
      messages_per_month: 50,
    },
    features: [
      "Basic World Settings",
      "Community Support",
    ],
    badge: {
      icon: '🆓',
      color: 'gray',
    },
    order: 1,
  },

  pro: {
    tier: 'pro',
    name: 'pro',
    displayName: 'Pro',
    price: {
      monthly: 999, // $9.99
      yearly: 9999, // $99.99 (2 months free)
    },
    description: 'Enhanced features for dedicated solo RPG enthusiasts',
    limits: {
      worlds_per_week: 5,
      messages_per_month: 100,
    },
    features: [
      "Edit Data",
      "Priority Support",
      "Export Data",
      "Early Access Features",
    ],
    badge: {
      icon: '⭐',
      color: 'primary',
    },
    popular: true,
    order: 2,
  },

  pro_plus: {
    tier: 'pro_plus',
    name: 'pro_plus',
    displayName: 'Pro+',
    price: {
      monthly: 1999, // $19.99
      yearly: 19999, // $199.99 (2 months free)
    },
    description: 'Ultimate experience with unlimited worlds and advanced AI',
    limits: {
      worlds_per_week: 10,
      messages_per_month: 500,
    },
    features: [
      "Edit Data",
      "Priority Support",
      "Export Data",
      "Early Access Features",
      "Advanced AI Models",
    ],
    badge: {
      icon: '👑',
      color: 'accent',
      gradient: true,
    },
    order: 3,
  },
};

/**
 * Get all subscription tiers in display order
 */
export function getAllTiers(): SubscriptionTierConfig[] {
  return Object.values(SUBSCRIPTION_TIERS).sort((a, b) => a.order - b.order);
}

/**
 * Get configuration for a specific tier
 */
export function getTierConfig(tier: SubscriptionTier): SubscriptionTierConfig {
  const config = SUBSCRIPTION_TIERS[tier];
  if (!config) {
    throw new Error(`Invalid subscription tier: ${tier}`);
  }
  return config;
}

/**
 * Get the message limit for a specific tier
 */
export function getMessageLimit(tier: SubscriptionTier): number {
  const config = getTierConfig(tier);
  return config.limits.messages_per_month;
}

/**
 * Get the worlds per week limit for a specific tier
 */
export function getWorldsPerWeekLimit(tier: SubscriptionTier): number {
  const config = getTierConfig(tier);
  return config.limits.worlds_per_week;
}

/**
 * Format price for display
 */
export function formatPrice(priceInCents: number): string {
  if (priceInCents === 0) return 'Free';
  return `$${(priceInCents / 100).toFixed(2)}`;
}

/**
 * Get monthly price for a tier
 */
export function getMonthlyPrice(tier: SubscriptionTier): string {
  const config = getTierConfig(tier);
  return formatPrice(config.price.monthly);
}

/**
 * Get yearly price for a tier (if available)
 */
export function getYearlyPrice(tier: SubscriptionTier): string | null {
  const config = getTierConfig(tier);
  return config.price.yearly ? formatPrice(config.price.yearly) : null;
}

/**
 * Calculate yearly savings
 */
export function getYearlySavings(tier: SubscriptionTier): number {
  const config = getTierConfig(tier);
  if (!config.price.yearly) return 0;

  const monthlyTotal = config.price.monthly * 12;
  return monthlyTotal - config.price.yearly;
}

/**
 * Validate subscription configuration
 */
export function validateConfig(): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check that all required tiers exist
  const requiredTiers: SubscriptionTier[] = ['free', 'pro', 'pro_plus'];
  for (const tier of requiredTiers) {
    if (!SUBSCRIPTION_TIERS[tier]) {
      errors.push(`Missing configuration for tier: ${tier}`);
    }
  }

  // Check that limits make sense (higher tiers should have higher or equal limits)
  const tiers = getAllTiers();
  for (let i = 1; i < tiers.length; i++) {
    const current = tiers[i];
    const previous = tiers[i - 1];

    if (current.limits.messages_per_month < previous.limits.messages_per_month) {
      errors.push(`${current.tier} has fewer messages_per_month than ${previous.tier}`);
    }

    if (current.limits.worlds_per_week < previous.limits.worlds_per_week) {
      errors.push(`${current.tier} has fewer worlds_per_week than ${previous.tier}`);
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}
