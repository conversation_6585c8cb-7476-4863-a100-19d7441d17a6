import { SubscriptionTier } from './schema.types';

// Core subscription configuration types
export interface SubscriptionLimits {
  worlds_per_week: number;
  messages_per_month: number;
}

export interface SubscriptionTierConfig {
  tier: SubscriptionTier;
  name: string;
  displayName: string;
  price: {
    monthly: number; // in cents (e.g., 999 = $9.99)
    yearly?: number; // optional yearly pricing
  };
  description: string;
  limits: SubscriptionLimits;
  features: string[]; // Array of feature descriptions
  badge: {
    icon: string;
    color: 'gray' | 'primary' | 'accent';
    gradient?: boolean;
  };
  popular?: boolean; // highlight as popular choice
  order: number; // display order
}

// Usage tracking types - aligned with add_usage_tracking.sql
export interface UserUsage {
  id: string;
  user_id: string;
  messages_this_month: number;
  messages_reset_date: string; // ISO date string
  worlds_this_week: number;
  worlds_reset_date: string; // ISO date string
  created_at: string;
  updated_at: string;
}

export interface UsageCheckResult {
  allowed: boolean;
  current: number;
  limit: number;
  remaining: number;
  resetDate?: string; // for monthly limits
  message?: string; // user-friendly message
}

export interface UserUsageStats {
  messages_monthly: UsageCheckResult;
  worlds_weekly: UsageCheckResult;
  subscription_tier: SubscriptionTier;
}

// API response types
export interface SubscriptionInfoResponse {
  user_id: string;
  subscription_tier: SubscriptionTier;
  config: SubscriptionTierConfig;
  usage: UserUsageStats;
}

// Limit check types for different actions
export type LimitType = 'messages' | 'worlds';

export interface LimitCheckOptions {
  userId: string;
  limitType: LimitType;
  increment?: boolean; // whether to increment usage after check
}

// Error types for limit enforcement
export class SubscriptionLimitError extends Error {
  constructor(
    public limitType: LimitType,
    public current: number,
    public limit: number,
    public tier: SubscriptionTier,
    message?: string
  ) {
    super(message || `${limitType} limit exceeded: ${current}/${limit}`);
    this.name = 'SubscriptionLimitError';
  }
}

// Configuration validation types
export interface ConfigValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}
