import { NextRequest, NextResponse } from 'next/server';
import { requireUserSession, requireSelfOrAdmin, requireAdmin } from '@/lib/auth-helpers';
import { updateUserSubscriptionTier } from '@/lib/user-service';
import { SubscriptionTier } from '@/types/schema.types';
import { getTierConfig } from '@/config/subscription.config';
import { getUserUsageStats } from '@/lib/usage-tracking.service';

/**
 * GET /api/v1/users/[userId]/subscription
 * Get user's subscription information
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const userId = params.userId;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    await requireSelfOrAdmin(session, targetUserId);

    // Get subscription tier and configuration
    const subscriptionTier = session.user.subscription_tier || 'free';
    const tierConfig = getTierConfig(subscriptionTier);

    // Get usage statistics
    const usageStats = await getUserUsageStats(targetUserId);

    return NextResponse.json({
      user_id: targetUserId,
      subscription_tier: subscriptionTier,
      config: tierConfig,
      usage: usageStats
    });
  } catch (error) {
    console.error('Error getting user subscription:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/v1/users/[userId]/subscription
 * Update user's subscription tier (admin only for now)
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await requireUserSession(request);
    const userId = params.userId;
    const targetUserId = userId === 'me' ? session.user.id : userId;
    await requireAdmin(session);
    // In the future, this could be integrated with payment processing
    const body = await request.json();
    const { subscription_tier } = body;

    // Validate subscription tier
    const validTiers: SubscriptionTier[] = ['free', 'pro', 'pro_plus'];
    if (!validTiers.includes(subscription_tier)) {
      return NextResponse.json(
        { error: 'Invalid subscription tier. Must be one of: free, pro, pro_plus' },
        { status: 400 }
      );
    }

    // Update the subscription tier
    const updatedUser = await updateUserSubscriptionTier(targetUserId, subscription_tier);

    if (!updatedUser) {
      return NextResponse.json(
        { error: 'Failed to update subscription tier' },
        { status: 500 }
      );
    }

    const tierConfig = getTierConfig(updatedUser.subscription_tier);

    return NextResponse.json({
      user_id: targetUserId,
      subscription_tier: updatedUser.subscription_tier,
      config: tierConfig,
      updated_at: updatedUser.updated_at
    });
  } catch (error) {
    console.error('Error updating user subscription:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}


